using FleetXQ.Application.Interfaces;
using FleetXQ.Domain.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;

namespace FleetXQ.Api.Hubs;

/// <summary>
/// SignalR hub for real-time alert notifications
/// </summary>
[Authorize]
public class AlertHub : BaseFleetXQHub
{
    private readonly ISignalRConnectionManager _connectionManager;

    public AlertHub(
        ISignalRConnectionManager connectionManager,
        ILogger<AlertHub> logger) : base(logger)
    {
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
    }

    /// <summary>
    /// Subscribe to real-time alert notifications
    /// </summary>
    /// <param name="severityFilter">Optional severity filter (Critical, High, Medium, Low)</param>
    /// <param name="typeFilter">Optional alert type filter</param>
    /// <returns>Task representing the async operation</returns>
    public async Task SubscribeToAlerts(string? severityFilter = null, string? typeFilter = null)
    {
        await ExecuteSafelyAsync(async () =>
        {
            var userId = GetCurrentUserId();
            var connectionId = GetConnectionId();
            var userRoles = GetCurrentUserRoles();

            // Determine the appropriate group based on user role and filters
            var groupName = DetermineAlertGroup(userRoles, severityFilter, typeFilter);

            // Add connection to alerts group
            await Groups.AddToGroupAsync(connectionId, groupName);
            
            // Track the subscription in connection manager
            await _connectionManager.AddToGroupAsync(userId, groupName);

            Logger.LogInformation("User {UserId} with roles [{Roles}] subscribed to alerts group {GroupName}", 
                userId, string.Join(", ", userRoles), groupName);

            // Send confirmation to the caller
            await Clients.Caller.SendAsync("AlertSubscriptionConfirmed", new
            {
                GroupName = groupName,
                SeverityFilter = severityFilter,
                TypeFilter = typeFilter,
                Message = $"Successfully subscribed to alert notifications"
            });

        }, nameof(SubscribeToAlerts));
    }

    /// <summary>
    /// Unsubscribe from alert notifications
    /// </summary>
    /// <param name="severityFilter">Optional severity filter to unsubscribe from</param>
    /// <param name="typeFilter">Optional alert type filter to unsubscribe from</param>
    /// <returns>Task representing the async operation</returns>
    public async Task UnsubscribeFromAlerts(string? severityFilter = null, string? typeFilter = null)
    {
        await ExecuteSafelyAsync(async () =>
        {
            var userId = GetCurrentUserId();
            var connectionId = GetConnectionId();
            var userRoles = GetCurrentUserRoles();

            // Determine the group to unsubscribe from
            var groupName = DetermineAlertGroup(userRoles, severityFilter, typeFilter);

            // Remove connection from alerts group
            await Groups.RemoveFromGroupAsync(connectionId, groupName);
            
            // Remove the subscription from connection manager
            await _connectionManager.RemoveFromGroupAsync(userId, groupName);

            Logger.LogInformation("User {UserId} unsubscribed from alerts group {GroupName}", 
                userId, groupName);

            // Send confirmation to the caller
            await Clients.Caller.SendAsync("AlertUnsubscriptionConfirmed", new
            {
                GroupName = groupName,
                SeverityFilter = severityFilter,
                TypeFilter = typeFilter,
                Message = $"Successfully unsubscribed from alert notifications"
            });

        }, nameof(UnsubscribeFromAlerts));
    }

    /// <summary>
    /// Subscribe to alerts for a specific vehicle
    /// </summary>
    /// <param name="vehicleId">The vehicle ID to subscribe to alerts for</param>
    /// <returns>Task representing the async operation</returns>
    public async Task SubscribeToVehicleAlerts(Guid vehicleId)
    {
        await ExecuteSafelyAsync(async () =>
        {
            if (vehicleId == Guid.Empty)
            {
                throw new ArgumentException("Vehicle ID cannot be empty", nameof(vehicleId));
            }

            var userId = GetCurrentUserId();
            var connectionId = GetConnectionId();
            var groupName = $"VehicleAlerts_{vehicleId}";

            // Add connection to vehicle-specific alerts group
            await Groups.AddToGroupAsync(connectionId, groupName);
            
            // Track the subscription in connection manager
            await _connectionManager.AddToGroupAsync(userId, groupName);

            Logger.LogInformation("User {UserId} subscribed to alerts for vehicle {VehicleId}", 
                userId, vehicleId);

            // Send confirmation to the caller
            await Clients.Caller.SendAsync("VehicleAlertSubscriptionConfirmed", new
            {
                VehicleId = vehicleId,
                Message = $"Successfully subscribed to alerts for vehicle {vehicleId}"
            });

        }, nameof(SubscribeToVehicleAlerts));
    }

    /// <summary>
    /// Unsubscribe from alerts for a specific vehicle
    /// </summary>
    /// <param name="vehicleId">The vehicle ID to unsubscribe from alerts for</param>
    /// <returns>Task representing the async operation</returns>
    public async Task UnsubscribeFromVehicleAlerts(Guid vehicleId)
    {
        await ExecuteSafelyAsync(async () =>
        {
            if (vehicleId == Guid.Empty)
            {
                throw new ArgumentException("Vehicle ID cannot be empty", nameof(vehicleId));
            }

            var userId = GetCurrentUserId();
            var connectionId = GetConnectionId();
            var groupName = $"VehicleAlerts_{vehicleId}";

            // Remove connection from vehicle-specific alerts group
            await Groups.RemoveFromGroupAsync(connectionId, groupName);
            
            // Remove the subscription from connection manager
            await _connectionManager.RemoveFromGroupAsync(userId, groupName);

            Logger.LogInformation("User {UserId} unsubscribed from alerts for vehicle {VehicleId}", 
                userId, vehicleId);

            // Send confirmation to the caller
            await Clients.Caller.SendAsync("VehicleAlertUnsubscriptionConfirmed", new
            {
                VehicleId = vehicleId,
                Message = $"Successfully unsubscribed from alerts for vehicle {vehicleId}"
            });

        }, nameof(UnsubscribeFromVehicleAlerts));
    }

    /// <summary>
    /// Called when a connection is established
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        await base.OnConnectedAsync();
        
        var userId = GetCurrentUserId();
        var connectionId = GetConnectionId();
        
        // Register the connection
        await _connectionManager.AddConnectionAsync(userId, connectionId);
        
        Logger.LogInformation("User {UserId} connected to AlertHub with connection {ConnectionId}", 
            userId, connectionId);
    }

    /// <summary>
    /// Called when a connection is terminated
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = GetCurrentUserId();
        var connectionId = GetConnectionId();
        
        // Unregister the connection and clean up group memberships
        await _connectionManager.RemoveConnectionAsync(userId, connectionId);
        
        // Get all groups the user was subscribed to and clean them up
        var userGroups = await _connectionManager.GetUserGroupsAsync(userId);
        foreach (var groupName in userGroups)
        {
            await _connectionManager.RemoveFromGroupAsync(userId, groupName);
        }
        
        Logger.LogInformation("User {UserId} disconnected from AlertHub, cleaned up {GroupCount} group subscriptions", 
            userId, userGroups.Count());
        
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Determines the appropriate alert group based on user roles and filters
    /// </summary>
    private static string DetermineAlertGroup(IEnumerable<string> userRoles, string? severityFilter, string? typeFilter)
    {
        var roles = userRoles.ToList();
        
        // Build group name based on filters
        var groupParts = new List<string> { "Alerts" };
        
        if (!string.IsNullOrEmpty(severityFilter) && Enum.TryParse<AlertSeverity>(severityFilter, true, out var severity))
        {
            groupParts.Add($"Severity_{severity}");
        }
        
        if (!string.IsNullOrEmpty(typeFilter) && Enum.TryParse<AlertType>(typeFilter, true, out var alertType))
        {
            groupParts.Add($"Type_{alertType}");
        }

        // Add role-based filtering if needed
        if (roles.Contains("Driver", StringComparer.OrdinalIgnoreCase))
        {
            groupParts.Add("Driver");
        }
        else if (roles.Contains("Manager", StringComparer.OrdinalIgnoreCase))
        {
            groupParts.Add("Manager");
        }
        else if (roles.Contains("Admin", StringComparer.OrdinalIgnoreCase))
        {
            groupParts.Add("Admin");
        }

        return string.Join("_", groupParts);
    }
}

using FleetXQ.Application.Features.Telemetry.Queries.GetLatestTelemetry;
using FleetXQ.Application.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;

namespace FleetXQ.Api.Hubs;

/// <summary>
/// SignalR hub for real-time vehicle telemetry data
/// </summary>
[Authorize]
public class TelemetryHub : BaseFleetXQHub
{
    private readonly IMediator _mediator;
    private readonly ISignalRConnectionManager _connectionManager;

    public TelemetryHub(
        IMediator mediator,
        ISignalRConnectionManager connectionManager,
        ILogger<TelemetryHub> logger) : base(logger)
    {
        _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
    }

    /// <summary>
    /// Subscribe to real-time telemetry updates for a specific vehicle
    /// </summary>
    /// <param name="vehicleId">The vehicle ID to subscribe to</param>
    /// <returns>Task representing the async operation</returns>
    public async Task SubscribeToVehicle(Guid vehicleId)
    {
        await ExecuteSafelyAsync(async () =>
        {
            if (vehicleId == Guid.Empty)
            {
                throw new ArgumentException("Vehicle ID cannot be empty", nameof(vehicleId));
            }

            var userId = GetCurrentUserId();
            var connectionId = GetConnectionId();
            var groupName = $"Vehicle_{vehicleId}";

            // Add connection to vehicle-specific group
            await Groups.AddToGroupAsync(connectionId, groupName);
            
            // Track the subscription in connection manager
            await _connectionManager.AddToGroupAsync(userId, groupName);

            Logger.LogInformation("User {UserId} subscribed to vehicle {VehicleId} telemetry updates", 
                userId, vehicleId);

            // Send confirmation to the caller
            await Clients.Caller.SendAsync("SubscriptionConfirmed", new
            {
                Type = "Vehicle",
                VehicleId = vehicleId,
                Message = $"Successfully subscribed to vehicle {vehicleId} telemetry updates"
            });

            // Optionally send the latest telemetry data immediately
            await SendLatestTelemetryToSubscriber(vehicleId);

        }, nameof(SubscribeToVehicle));
    }

    /// <summary>
    /// Unsubscribe from real-time telemetry updates for a specific vehicle
    /// </summary>
    /// <param name="vehicleId">The vehicle ID to unsubscribe from</param>
    /// <returns>Task representing the async operation</returns>
    public async Task UnsubscribeFromVehicle(Guid vehicleId)
    {
        await ExecuteSafelyAsync(async () =>
        {
            if (vehicleId == Guid.Empty)
            {
                throw new ArgumentException("Vehicle ID cannot be empty", nameof(vehicleId));
            }

            var userId = GetCurrentUserId();
            var connectionId = GetConnectionId();
            var groupName = $"Vehicle_{vehicleId}";

            // Remove connection from vehicle-specific group
            await Groups.RemoveFromGroupAsync(connectionId, groupName);
            
            // Remove the subscription from connection manager
            await _connectionManager.RemoveFromGroupAsync(userId, groupName);

            Logger.LogInformation("User {UserId} unsubscribed from vehicle {VehicleId} telemetry updates", 
                userId, vehicleId);

            // Send confirmation to the caller
            await Clients.Caller.SendAsync("UnsubscriptionConfirmed", new
            {
                Type = "Vehicle",
                VehicleId = vehicleId,
                Message = $"Successfully unsubscribed from vehicle {vehicleId} telemetry updates"
            });

        }, nameof(UnsubscribeFromVehicle));
    }

    /// <summary>
    /// Get the current status and latest telemetry data for a specific vehicle
    /// </summary>
    /// <param name="vehicleId">The vehicle ID to get status for</param>
    /// <returns>Task representing the async operation</returns>
    public async Task GetVehicleStatus(Guid vehicleId)
    {
        await ExecuteSafelyAsync(async () =>
        {
            if (vehicleId == Guid.Empty)
            {
                throw new ArgumentException("Vehicle ID cannot be empty", nameof(vehicleId));
            }

            var userId = GetCurrentUserId();
            
            Logger.LogDebug("User {UserId} requested status for vehicle {VehicleId}", userId, vehicleId);

            // Query the latest telemetry data using CQRS
            var query = new GetLatestTelemetryQuery
            {
                VehicleId = vehicleId,
                MaxAgeHours = 24 // Get telemetry data from the last 24 hours
            };

            var result = await _mediator.Send(query);

            if (result.Success && result.Telemetry != null)
            {
                // Send the current vehicle status to the caller
                await Clients.Caller.SendAsync("VehicleStatus", new
                {
                    VehicleId = vehicleId,
                    Status = "Active", // This could be derived from telemetry data
                    Telemetry = result.Telemetry,
                    LastUpdate = result.Telemetry.Timestamp,
                    IsOnline = DateTime.UtcNow.Subtract(result.Telemetry.Timestamp).TotalMinutes < 30
                });

                Logger.LogInformation("Successfully sent vehicle status for {VehicleId} to user {UserId}", 
                    vehicleId, userId);
            }
            else
            {
                // Send offline status if no recent telemetry data
                await Clients.Caller.SendAsync("VehicleStatus", new
                {
                    VehicleId = vehicleId,
                    Status = "Offline",
                    Telemetry = (object?)null,
                    LastUpdate = (DateTime?)null,
                    IsOnline = false,
                    Message = result.ErrorMessage ?? "No recent telemetry data available"
                });

                Logger.LogInformation("Vehicle {VehicleId} appears offline - no recent telemetry data", vehicleId);
            }

        }, nameof(GetVehicleStatus));
    }

    /// <summary>
    /// Called when a connection is established
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        await base.OnConnectedAsync();
        
        var userId = GetCurrentUserId();
        var connectionId = GetConnectionId();
        
        // Register the connection
        await _connectionManager.AddConnectionAsync(userId, connectionId);
        
        Logger.LogInformation("User {UserId} connected to TelemetryHub with connection {ConnectionId}", 
            userId, connectionId);
    }

    /// <summary>
    /// Called when a connection is terminated
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = GetCurrentUserId();
        var connectionId = GetConnectionId();
        
        // Unregister the connection and clean up group memberships
        await _connectionManager.RemoveConnectionAsync(userId, connectionId);
        
        // Get all groups the user was subscribed to and clean them up
        var userGroups = await _connectionManager.GetUserGroupsAsync(userId);
        foreach (var groupName in userGroups)
        {
            await _connectionManager.RemoveFromGroupAsync(userId, groupName);
        }
        
        Logger.LogInformation("User {UserId} disconnected from TelemetryHub, cleaned up {GroupCount} group subscriptions", 
            userId, userGroups.Count());
        
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Sends the latest telemetry data to a new subscriber
    /// </summary>
    private async Task SendLatestTelemetryToSubscriber(Guid vehicleId)
    {
        try
        {
            var query = new GetLatestTelemetryQuery
            {
                VehicleId = vehicleId,
                MaxAgeHours = 1 // Only send very recent data
            };

            var result = await _mediator.Send(query);

            if (result.Success && result.Telemetry != null)
            {
                await Clients.Caller.SendAsync("TelemetryUpdate", result.Telemetry);
                Logger.LogDebug("Sent latest telemetry data for vehicle {VehicleId} to new subscriber", vehicleId);
            }
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Failed to send latest telemetry data for vehicle {VehicleId} to new subscriber", vehicleId);
        }
    }
}
